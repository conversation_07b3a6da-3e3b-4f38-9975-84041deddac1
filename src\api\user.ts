// 用户相关API接口
import { api, type ApiResponse } from '@/utils/request'

// 用户信息接口
export interface UserInfo {
  id: number
  openid: string
  unionid?: string
  nickname: string
  avatar_url: string
  meditation_level: number
  streak_days: number
}

// 登录请求参数
export interface LoginParams {
  openid: string
  unionid?: string
  nickname: string
  avatar_url: string
}

// 登录响应数据
export interface LoginResponse {
  token: string
  user: UserInfo
}

// 微信登录code换取openid的参数
export interface WxCodeParams {
  code: string
}

// 微信登录code换取openid的响应
export interface WxCodeResponse {
  openid: string
  session_key: string
  unionid?: string
}

// 用户API类
export class UserApi {
  // 微信小程序登录
  static async login(params: LoginParams): Promise<ApiResponse<LoginResponse>> {
    return api.post<LoginResponse>('/api/public/user/login', params, {
      needAuth: false // 登录接口不需要token
    })
  }

  // 微信code换取openid (如果后端提供此接口)
  static async getOpenidByCode(params: WxCodeParams): Promise<ApiResponse<WxCodeResponse>> {
    return api.post<WxCodeResponse>('/api/public/user/code2session', params, {
      needAuth: false
    })
  }

  // 获取用户信息
  static async getUserInfo(): Promise<ApiResponse<UserInfo>> {
    return api.get<UserInfo>('/api/user/info')
  }

  // 更新用户信息
  static async updateUserInfo(params: Partial<UserInfo>): Promise<ApiResponse<UserInfo>> {
    return api.put<UserInfo>('/api/user/info', params)
  }

  // 退出登录
  static async logout(): Promise<ApiResponse<null>> {
    return api.post<null>('/api/user/logout')
  }
}
