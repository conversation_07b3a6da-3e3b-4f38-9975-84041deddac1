<template>
  <view class="profile-page">
    <NavBar title="我的" :show-back="false" />

    <scroll-view class="content" scroll-y>
      <!-- 上部分：个人信息 -->
      <view class="profile-header">
        <view class="user-info-card">
          <view class="avatar-section" :class="{ 'not-logged-in': !userInfo.isLoggedIn, 'logged-in': userInfo.isLoggedIn }">
            <!-- 未登录状态 -->
            <template v-if="!userInfo.isLoggedIn">
              <view class="welcome-section">
                <text class="welcome-text">嗨，多肉冥想的新朋友</text>
              </view>
              <view class="login-btn" @click="login">
                <text class="login-text">立即登录</text>
              </view>
            </template>

            <!-- 已登录状态 -->
            <template v-else>
              <image class="avatar" :src="userInfo.avatarUrl" mode="aspectFill" />
              <view class="user-basic">
                <text class="username">{{ userInfo.nickname }}</text>
              </view>
            </template>
          </view>

          <view class="stats-grid">
            <view class="stat-item">
              <text class="stat-number">{{ userInfo.succulentCount }}</text>
              <text class="stat-label">我的多肉</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-number">Lv.{{ userInfo.meditationLevel }}</text>
              <text class="stat-label">冥想等级</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-number">{{ userInfo.totalDays }}</text>
              <text class="stat-label">坚持天数</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 中部分：功能入口和数据展示 -->
      <view class="profile-middle">
        <!-- 会员开通入口 -->
        <view class="vip-card" @click="openVip">
          <view class="vip-content">
            <view class="vip-info">
              <text class="vip-title">开通会员</text>
              <text class="vip-desc">解锁更多多肉品种和冥想内容</text>
            </view>
            <view class="vip-icon">
              <text class="crown-icon">👑</text>
            </view>
          </view>
        </view>

        <!-- 功能入口网格 -->
        <view class="function-grid">
          <view class="function-item" @click="goToMyFavorites">
            <view class="function-icon favorite-bg">
              <text class="icon">❤️</text>
            </view>
            <text class="function-label">我的收藏</text>
          </view>

          <view class="function-item" @click="goToSucculentSpace">
            <view class="function-icon succulent-bg">
              <text class="icon">🌱</text>
            </view>
            <text class="function-label">多肉空间</text>
          </view>

          <view class="function-item" @click="goToMyData">
            <view class="function-icon data-bg">
              <text class="icon">📊</text>
            </view>
            <text class="function-label">我的数据</text>
          </view>
        </view>

        <!-- 数据概览卡片 -->
        <view class="data-overview-card">
          <view class="card-header">
            <text class="card-title">本周数据</text>
            <text class="more-btn" @click="goToMyData">查看更多</text>
          </view>
          <view class="data-items">
            <view class="data-item">
              <text class="data-label">冥想时长</text>
              <text class="data-value">{{ weekData.meditationTime }}分钟</text>
            </view>
            <view class="data-item">
              <text class="data-label">获得能量</text>
              <text class="data-value">{{ weekData.energy }}</text>
            </view>
            <view class="data-item">
              <text class="data-label">完成任务</text>
              <text class="data-value">{{ weekData.completedTasks }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 下部分：菜单列表 -->
      <view class="profile-bottom">
        <view class="menu-card">
          <view class="menu-item" @click="followWechat">
            <view class="menu-icon">
              <text class="icon">📱</text>
            </view>
            <text class="menu-label">关注公众号</text>
            <text class="menu-arrow">></text>
          </view>

          <view class="menu-divider"></view>

          <view class="menu-item" @click="goToAbout">
            <view class="menu-icon">
              <text class="icon">ℹ️</text>
            </view>
            <text class="menu-label">关于我们</text>
            <text class="menu-arrow">></text>
          </view>

          <view class="menu-divider"></view>

          <view class="menu-item" @click="goToFeedback">
            <view class="menu-icon">
              <text class="icon">💬</text>
            </view>
            <text class="menu-label">意见反馈</text>
            <text class="menu-arrow">></text>
          </view>

          <view class="menu-divider"></view>

          <view class="menu-item" @click="goToSettings">
            <view class="menu-icon">
              <text class="icon">⚙️</text>
            </view>
            <text class="menu-label">设置</text>
            <text class="menu-arrow">></text>
          </view>
        </view>
      </view>
    </scroll-view>

    <TabBar />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NavBar from '@/components/NavBar.vue'
import TabBar from '@/components/TabBar.vue'
import {onLoad, onShow} from '@dcloudio/uni-app'
import { useAuth } from '@/composables/useAuth'

// 用户信息
const userInfo = ref({
  avatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132",
  nickname: '多肉爱好者',
  id: 'USER001',
  succulentCount: 12,
  meditationLevel: 5,
  totalDays: 28,
  isLoggedIn: false
})
const auth = useAuth()

// 本周数据
const weekData = ref({
  meditationTime: 180,
  energy: 1250,
  completedTasks: 15
})

// 登录方法 - 必须在用户点击事件中调用
const login = () => {
  uni.showLoading({ title: '登录中...' })

  // 必须在用户点击事件中同步调用登录方法
  auth.login().then((user) => {
    // 更新本地用户信息显示
    userInfo.value = {
      ...userInfo.value,
      avatarUrl: user.avatar_url,
      nickname: user.nickname,
      isLoggedIn: true,
      id: user.id.toString(),
      meditationLevel: user.meditation_level,
      totalDays: user.streak_days
    }

    uni.hideLoading()
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
  }).catch((error) => {
    uni.hideLoading()
    console.error('登录失败:', error)
    uni.showToast({
      title: error instanceof Error ? error.message : '登录失败',
      icon: 'none'
    })
  })
}

// 功能方法
const openVip = () => {
  uni.showToast({ title: '开通会员功能开发中', icon: 'none' })
}

const goToMyFavorites = () => {
  uni.navigateTo({ url: '/pages/favorites/favorites' })
}

const goToSucculentSpace = () => {
  uni.navigateTo({ url: '/pages/succulent/space' })
}

const goToMyData = () => {
  uni.navigateTo({ url: '/pages/data/index' })
}

const followWechat = () => {
  uni.showModal({
    title: '关注公众号',
    content: '请在微信中搜索"多肉冥想"关注我们的公众号',
    showCancel: false
  })
}

const goToAbout = () => {
  uni.navigateTo({ url: '/pages/about/index' })
}

const goToFeedback = () => {
  uni.navigateTo({ url: '/pages/feedback/index' })
}

const goToSettings = () => {
  uni.navigateTo({ url: '/pages/settings/index' })
}


// 检查并更新用户登录状态的函数
const checkUserLoginStatus = () => {
  // 从store中获取用户信息
  if (auth.isLoggedIn && auth.userInfo) {
    userInfo.value = {
      ...userInfo.value,
      avatarUrl: auth.userInfo.avatar_url || userInfo.value.avatarUrl,
      nickname: auth.userInfo.nickname || userInfo.value.nickname,
      isLoggedIn: true,
      id: auth.userInfo.id.toString(),
      meditationLevel: auth.userInfo.meditation_level,
      totalDays: auth.userInfo.streak_days
    }
  } else {
    // 如果没有登录，保持未登录状态
    userInfo.value = {
      ...userInfo.value,
      isLoggedIn: false
    }
  }
}

onLoad(() => {
  checkUserLoginStatus()
})

onShow(() => {
  // 页面显示时重新检查登录状态
  checkUserLoginStatus()
})

</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background-color: #F7FAFC;
}

.content {
  padding-top: 176rpx;
  padding-bottom: 100rpx;
  height: calc(100vh - 276rpx);
}

/* 上部分：个人信息 */
.profile-header {
  padding: 32rpx 32rpx 48rpx;
}

.user-info-card {
  background: #FFFFFF;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 48rpx;
}

/* 未登录状态的布局 */
.avatar-section.not-logged-in {
  justify-content: space-between;
}

/* 已登录状态的布局 */
.avatar-section.logged-in {
  justify-content: flex-start;
}

.welcome-section {
  flex: 1;
}

.welcome-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #2D3748;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 32rpx;
}

.user-basic {
  flex: 1;
}

.username {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #2D3748;
  margin-bottom: 8rpx;
}

.user-id {
  font-size: 24rpx;
  color: #718096;
}

.login-btn {
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #7FB069 0%, #A8D08D 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 160rpx;
}

.login-text {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
}



.stats-grid {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: 700;
  color: #7FB069;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #4A5568;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background-color: #E2E8F0;
}

/* 中部分：功能入口 */
.profile-middle {
  padding: 0 32rpx 48rpx;
}

.vip-card {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.vip-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.vip-info {
  flex: 1;
}

.vip-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 8rpx;
}

.vip-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.vip-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.crown-icon {
  font-size: 60rpx;
}

.function-grid {
  display: flex;
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.function-item {
  flex: 1;
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.function-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.function-icon .icon {
  font-size: 48rpx;
}

.favorite-bg {
  background: linear-gradient(135deg, #FF6B9D 0%, #FF8E9B 100%);
}

.succulent-bg {
  background: linear-gradient(135deg, #7FB069 0%, #A8D08D 100%);
}

.data-bg {
  background: linear-gradient(135deg, #F4A261 0%, #E76F51 100%);
}

.function-label {
  font-size: 24rpx;
  color: #4A5568;
  text-align: center;
}

.data-overview-card {
  background: #FFFFFF;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2D3748;
}

.more-btn {
  font-size: 24rpx;
  color: #7FB069;
}

.data-items {
  display: flex;
  justify-content: space-around;
}

.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.data-label {
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 8rpx;
}

.data-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #2D3748;
}

/* 下部分：菜单列表 */
.profile-bottom {
  padding: 0 32rpx 48rpx;
}

.menu-card {
  background: #FFFFFF;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 48rpx 32rpx;
  transition: all 0.15s ease;
}

.menu-item:active {
  background-color: #F1F5F9;
}

.menu-icon {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.menu-icon .icon {
  font-size: 36rpx;
}

.menu-label {
  flex: 1;
  font-size: 28rpx;
  color: #2D3748;
}

.menu-arrow {
  font-size: 32rpx;
  color: #A0AEC0;
}

.menu-divider {
  height: 2rpx;
  background-color: #F7FAFC;
  margin: 0 32rpx;
}
</style>