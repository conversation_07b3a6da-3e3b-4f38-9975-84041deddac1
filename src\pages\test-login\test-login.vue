<template>
  <view class="test-login-page">
    <NavBar title="登录测试" />
    
    <view class="content">
      <view class="section">
        <text class="section-title">用户状态</text>
        <view class="info-item">
          <text class="label">登录状态:</text>
          <text class="value" :class="{ success: auth.isLoggedIn }">
            {{ auth.isLoggedIn ? '已登录' : '未登录' }}
          </text>
        </view>
        <view class="info-item">
          <text class="label">OpenID:</text>
          <text class="value">{{ auth.openid || '无' }}</text>
        </view>
        <view class="info-item" v-if="auth.userInfo">
          <text class="label">昵称:</text>
          <text class="value">{{ auth.userInfo.nickname }}</text>
        </view>
        <view class="info-item" v-if="auth.userInfo">
          <text class="label">冥想等级:</text>
          <text class="value">{{ auth.userInfo.meditation_level }}</text>
        </view>
      </view>

      <view class="section">
        <text class="section-title">操作</text>
        <button 
          class="login-btn" 
          @click="handleLogin"
          :disabled="isLoading"
        >
          {{ isLoading ? '登录中...' : '微信登录' }}
        </button>
        
        <button 
          class="logout-btn" 
          @click="handleLogout"
          v-if="auth.isLoggedIn"
        >
          退出登录
        </button>
      </view>

      <view class="section" v-if="logs.length > 0">
        <text class="section-title">日志</text>
        <view class="logs">
          <text 
            class="log-item" 
            v-for="(log, index) in logs" 
            :key="index"
          >
            {{ log }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NavBar from '@/components/NavBar.vue'
import { useAuth } from '@/composables/useAuth'

const auth = useAuth()
const isLoading = ref(false)
const logs = ref<string[]>([])

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 10) {
    logs.value = logs.value.slice(0, 10)
  }
}

const handleLogin = () => {
  if (isLoading.value) return

  isLoading.value = true
  addLog('开始登录流程...')

  // 必须在用户点击事件中同步调用登录方法
  auth.login().then((userInfo) => {
    addLog(`登录成功: ${userInfo.nickname}`)
    addLog(`OpenID: ${userInfo.openid}`)
    addLog(`冥想等级: ${userInfo.meditation_level}`)
    isLoading.value = false
  }).catch((error) => {
    const errorMsg = error instanceof Error ? error.message : '登录失败'
    addLog(`登录失败: ${errorMsg}`)
    console.error('登录失败:', error)
    isLoading.value = false
  })
}

const handleLogout = () => {
  auth.logout()
  addLog('已退出登录')
}
</script>

<style scoped>
.test-login-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 176rpx;
}

.content {
  padding: 32rpx;
}

.section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
}

.value.success {
  color: #52c41a;
}

.login-btn, .logout-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  margin-bottom: 24rpx;
}

.login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.login-btn:disabled {
  opacity: 0.6;
}

.logout-btn {
  background: #f5f5f5;
  color: #666;
}

.logs {
  max-height: 400rpx;
  overflow-y: auto;
}

.log-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  padding: 8rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  word-break: break-all;
}

.log-item:last-child {
  border-bottom: none;
}
</style>
