<template>
  <view class="plan-page">
    <NavBar title="计划" :show-back="false" />

    <scroll-view class="content" scroll-y="true">
      <view class="content-wrapper">
        <!-- 日期导航 -->
        <view class="date-nav">
          <view v-for="date in dateList" :key="date.date" class="date-item" :class="{
            'active': date.date === currentDate,
            'today': date.isToday
          }" @click="selectDate(date.date)">
            <text class="date-day" :class="{ 'today-day': date.isToday }">{{ date.day }}</text>
            <text class="date-text" :class="{ 'today-text': date.isToday }">{{ date.text }}</text>
          </view>
        </view>

        <!-- 进度显示 -->
        <view class="progress-section">
          <view class="progress-header">
            <text class="progress-title">今日进度</text>
            <text class="progress-text">{{ completedCount }}/{{ totalCount }}</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
          </view>
        </view>

        <!-- 计划列表 -->
        <view v-if="currentPlans.length > 0" class="plan-list">
          <view class="section-header">
            <text class="section-title">{{ currentDateText }}计划</text>
            <text class="manage-plan-button" @click="onManagePlan">计划管理</text>
          </view>
          <PlanCard v-for="plan in currentPlans" :key="plan.id" :id="plan.id" :course-name="plan.courseName"
            :lesson-name="plan.lessonName" :cover="plan.cover" :duration="plan.duration" :progress="plan.progress"
            :learners-count="plan.learnersCount" :is-completed="plan.isCompleted" @click="onPlanClick" />
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-state">
          <text class="empty-title">暂无计划</text>
          <text class="empty-desc">为自己制定一个冥想计划吧</text>
          <view class="custom-plan-button" @click="onCustomPlan">
            <text class="button-text">定制计划</text>
          </view>
        </view>

        <!-- 推荐课程 -->
        <view class="recommend-section">
          <view class="section-header">
            <text class="section-title">推荐课程</text>
            <text class="view-all" @click="onViewAllRecommend">查看全部</text>
          </view>
          <scroll-view class="recommend-scroll" scroll-x="true" show-scrollbar="false">
            <view class="recommend-container">
              <RecommendCard v-for="course in recommendCourses" :key="course.id" :id="course.id" :title="course.title"
                :description="course.description" :cover="course.cover" :duration="course.duration"
                @click="onCourseClick" @add-to-plan="onAddToPlan" />
            </view>
          </scroll-view>
        </view>
      </view>
    </scroll-view>

    <TabBar />
  </view>
</template>

<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";

import { ref, computed } from 'vue'
import NavBar from '@/components/NavBar.vue'
import TabBar from '@/components/TabBar.vue'
import PlanCard from '@/components/PlanCard.vue'
import RecommendCard from '@/components/RecommendCard.vue'

// 获取当前真实日期
const getTodayDateString = () => {
  const today = new Date()
  return today.toISOString().split('T')[0]
}

// 获取星期几的中文
const getWeekdayText = (date: Date) => {
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return weekdays[date.getDay()]
}

// 日期相关
const todayDate = getTodayDateString()
const currentDate = ref(todayDate)

const dateList = computed(() => {
  const today = new Date()
  const dates = []

  for (let i = -3; i <= 3; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)

    const dateStr = date.toISOString().split('T')[0]
    const day = date.getDate()
    const weekday = getWeekdayText(date)

    dates.push({
      date: dateStr,
      day: day,
      text: weekday,
      isToday: dateStr === todayDate
    })
  }

  return dates
})

const currentDateText = computed(() => {
  const item = dateList.value.find(d => d.date === currentDate.value)
  if (item?.isToday) return '今日'

  const selectedDate = new Date(currentDate.value)
  const today = new Date()
  const diffTime = selectedDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === -1) return '昨日'
  if (diffDays === 1) return '明日'

  return `${selectedDate.getMonth() + 1}月${selectedDate.getDate()}日`
})

// 定义计划项类型
interface PlanItem {
  id: string
  courseName: string
  lessonName: string
  cover: string
  duration: number
  progress: number
  learnersCount: number
  isCompleted: boolean
}

// 生成示例计划数据
const generatePlanData = () => {
  const data: Record<string, PlanItem[]> = {}
  const today = new Date()

  // 为前后7天生成示例数据
  for (let i = -3; i <= 3; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)
    const dateStr = date.toISOString().split('T')[0]

    if (i === -3) {
      data[dateStr] = [
        {
          id: `${i}_1`,
          courseName: '正念冥想基础课',
          lessonName: '晨间冥想',
          cover: 'https://picsum.photos/120/120?random=1',
          duration: 10,
          progress: 100,
          learnersCount: 1234,
          isCompleted: true
        },
        {
          id: `${i}_2`,
          courseName: '工作减压课程',
          lessonName: '午休冥想',
          cover: 'https://picsum.photos/120/120?random=2',
          duration: 15,
          progress: 100,
          learnersCount: 856,
          isCompleted: true
        }
      ]
    } else if (i === -2) {
      data[dateStr] = [
        {
          id: `${i}_1`,
          courseName: '呼吸训练课程',
          lessonName: '基础呼吸练习',
          cover: 'https://picsum.photos/120/120?random=3',
          duration: 8,
          progress: 100,
          learnersCount: 2156,
          isCompleted: true
        }
      ]
    } else if (i === -1) {
      data[dateStr] = [
        {
          id: `${i}_1`,
          courseName: '身心放松课程',
          lessonName: '全身扫描冥想',
          cover: 'https://picsum.photos/120/120?random=4',
          duration: 20,
          progress: 100,
          learnersCount: 987,
          isCompleted: true
        }
      ]
    } else if (i === 0) { // 今天
      data[dateStr] = [
        {
          id: `${i}_1`,
          courseName: '正念冥想基础课',
          lessonName: '专注当下练习',
          cover: 'https://picsum.photos/120/120?random=5',
          duration: 20,
          progress: 100,
          learnersCount: 1234,
          isCompleted: true
        },
        {
          id: `${i}_2`,
          courseName: '身心放松课程',
          lessonName: '深度身体扫描',
          cover: 'https://picsum.photos/120/120?random=6',
          duration: 15,
          progress: 65,
          learnersCount: 987,
          isCompleted: false
        },
        {
          id: `${i}_3`,
          courseName: '睡眠改善课程',
          lessonName: '睡前放松冥想',
          cover: 'https://picsum.photos/120/120?random=7',
          duration: 12,
          progress: 0,
          learnersCount: 1567,
          isCompleted: false
        }
      ]
    } else if (i === 1) {
      data[dateStr] = [
        {
          id: `${i}_1`,
          courseName: '专注力提升课程',
          lessonName: '注意力集中训练',
          cover: 'https://picsum.photos/120/120?random=8',
          duration: 25,
          progress: 0,
          learnersCount: 2345,
          isCompleted: false
        }
      ]
    } else if (i === 2) {
      data[dateStr] = []
    } else {
      data[dateStr] = [
        {
          id: `${i}_1`,
          courseName: '情绪管理课程',
          lessonName: '感恩冥想练习',
          cover: 'https://picsum.photos/120/120?random=9',
          duration: 15,
          progress: 0,
          learnersCount: 1876,
          isCompleted: false
        }
      ]
    }
  }

  return data
}

// 计划数据
const planData = ref<Record<string, PlanItem[]>>(generatePlanData())

const currentPlans = computed(() => {
  return planData.value[currentDate.value] || []
})

const completedCount = computed(() => {
  return currentPlans.value.filter((plan: PlanItem) => plan.isCompleted).length
})

const totalCount = computed(() => {
  return currentPlans.value.length
})

const progressPercent = computed(() => {
  if (totalCount.value === 0) return 0
  return Math.round((completedCount.value / totalCount.value) * 100)
})

// 推荐课程数据
const recommendCourses = ref([
  {
    id: 'r1',
    title: '深度放松',
    description: '释放一天的疲惫，让身心得到完全放松',
    cover: 'https://picsum.photos/280/160?random=r1',
    duration: 18
  },
  {
    id: 'r2',
    title: '情绪平衡',
    description: '学会管理情绪，保持内心的平静与和谐',
    cover: 'https://picsum.photos/280/160?random=r2',
    duration: 22
  },
  {
    id: 'r3',
    title: '专注力提升',
    description: '通过冥想训练，提高工作和学习的专注力',
    cover: 'https://picsum.photos/280/160?random=r3',
    duration: 15
  },
  {
    id: 'r4',
    title: '感恩冥想',
    description: '培养感恩的心态，发现生活中的美好',
    cover: 'https://picsum.photos/280/160?random=r4',
    duration: 12
  }
])

// 事件处理
const selectDate = (date: string) => {
  currentDate.value = date
}

const onPlanClick = (id: string) => {
  console.log('点击计划:', id)
  // 跳转到冥想详情页面
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${id}`
  })
}

const onCustomPlan = () => {
  console.log('定制计划')
  // 这里可以跳转到计划定制页面
}

const onCourseClick = (id: string) => {
  console.log('点击推荐课程:', id)
  // 跳转到冥想详情页面
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${id}`
  })
}

const onAddToPlan = (id: string) => {
  console.log('加入计划:', id)
  // 这里可以添加课程到计划的逻辑
  uni.showToast({
    title: '已加入计划',
    icon: 'success'
  })
}

const onManagePlan = () => {
  console.log('进入计划管理页面')
  uni.navigateTo({
    url: '/pages/plan-management/plan-management'
  })
}

const onViewAllRecommend = () => {
  console.log('查看全部推荐课程')
  // 跳转到推荐课程列表页面
  uni.navigateTo({
    url: '/pages/recommend-list/recommend-list'
  })
}

onShow(() => {
  const app = getApp()
  console.log(app.globalData.userInfo)
})
</script>

<style scoped>
.plan-page {
  padding-top: 176rpx;
  /* 状态栏 + 导航栏高度 */
  min-height: 100vh;
  background-color: #F7FAFC;
  display: flex;
  flex-direction: column;
}

.content {
  padding-top: 32rpx;
  flex: 1;
  padding-bottom: 150rpx;
  /* TabBar高度 */
  background-color: #F7FAFC;
}

.content-wrapper {
  padding: 0 32rpx;
}

/* 日期导航 */
.date-nav {
  display: flex;
  justify-content: space-between;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.date-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
  transition: all 0.3s;
  position: relative;
}

.date-item.active {
  background: #7fb069;
}

.date-item.today:not(.active) {
  background: rgba(127, 176, 105, 0.1);
}

.date-day {
  font-size: 32rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.date-day.today-day {
  font-weight: 700;
  color: #7fb069;
}

.date-item.active .date-day {
  color: #ffffff;
  font-weight: 700;
}

.date-text {
  font-size: 22rpx;
  color: #718096;
}

.date-text.today-text {
  color: #7fb069;
  font-weight: 500;
}

.date-item.active .date-text {
  color: #ffffff;
}

/* 今天日期的小圆点标识 */
.date-item.today:not(.active)::after {
  content: '';
  position: absolute;
  bottom: 8rpx;
  width: 8rpx;
  height: 8rpx;
  background: #7fb069;
  border-radius: 50%;
}

/* 进度部分 */
.progress-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
}

.progress-text {
  font-size: 24rpx;
  color: #7fb069;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #e2e8f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #7fb069;
  border-radius: 4rpx;
  transition: width 0.3s;
}

/* 计划列表 */
.plan-list {
  margin-bottom: 32rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 24rpx;
}

/* 空状态 */
.empty-state {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 80rpx 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #718096;
  margin-bottom: 48rpx;
}

.custom-plan-button {
  width: 240rpx;
  height: 80rpx;
  background: #7fb069;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.button-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
}

/* 推荐课程 */
.recommend-section {
  margin-bottom: 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.view-all {
  font-size: 24rpx;
  color: #7fb069;
  font-weight: 500;
}

.manage-plan-button {
  font-size: 24rpx;
  color: #7fb069;
  font-weight: 500;
}

.recommend-scroll {
  white-space: nowrap;
  width: 100%;
}

/* 隐藏水平滚动条 */
.recommend-scroll::-webkit-scrollbar {
  display: none;
}

.recommend-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.recommend-container {
  display: flex;
  padding: 0 0 16rpx 0;
  width: max-content;
}
</style>