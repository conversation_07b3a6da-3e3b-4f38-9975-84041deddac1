// 冥想相关API接口
import { api, type ApiResponse } from '@/utils/request'

// 冥想内容接口
export interface MeditationItem {
  id: string
  title: string
  description: string
  cover: string
  duration: number // 时长（秒）
  type: 'meditation' | 'sleep' | 'sound' // 类型
  category: string // 分类
  tags: string[] // 标签
  learners: number // 学习人数
  is_favorite: boolean // 是否收藏
  created_at: string
  updated_at: string
}

// 冥想详情接口
export interface MeditationDetail extends MeditationItem {
  content: string // 详细内容
  audio_url?: string // 音频链接
  suitable_for: string // 适合人群
  effects: string // 课程效果
  instructor?: string // 指导老师
}

// 冥想标签接口
export interface MeditationTag {
  id: string
  name: string
  count: number // 使用该标签的内容数量
}

// 获取冥想列表的参数
export interface GetMeditationListParams {
  pageNum?: number
  pageSize?: number
  type?: 'meditation' | 'sleep' | 'sound'
  category?: string
  tags?: string[]
}

// 冥想列表响应
export interface MeditationListResponse {
  list: MeditationItem[]
  total: number
  pageNum: number
  pageSize: number
  totalPages: number
}

// 搜索冥想内容的参数
export interface SearchMeditationParams {
  keyword: string
  type?: 'meditation' | 'sleep' | 'sound'
  pageNum?: number
  pageSize?: number
}

// 收藏操作响应
export interface FavoriteResponse {
  is_favorite: boolean
  message: string
}

// 冥想API类
export class MeditationApi {
  // 获取冥想内容列表
  static async getList(params: GetMeditationListParams = {}): Promise<ApiResponse<MeditationListResponse>> {
    const queryParams = new URLSearchParams()
    
    if (params.pageNum) queryParams.append('pageNum', params.pageNum.toString())
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
    if (params.type) queryParams.append('type', params.type)
    if (params.category) queryParams.append('category', params.category)
    if (params.tags && params.tags.length > 0) {
      params.tags.forEach(tag => queryParams.append('tags', tag))
    }
    
    const queryString = queryParams.toString()
    const url = `/api/meditation/list${queryString ? `?${queryString}` : ''}`
    
    return api.get<MeditationListResponse>(url)
  }

  // 获取冥想内容详情
  static async getDetail(id: string): Promise<ApiResponse<MeditationDetail>> {
    return api.get<MeditationDetail>(`/api/meditation/${id}`)
  }

  // 收藏/取消收藏冥想内容
  static async toggleFavorite(id: string): Promise<ApiResponse<FavoriteResponse>> {
    return api.post<FavoriteResponse>(`/api/meditation/${id}/favorite`)
  }

  // 搜索冥想内容
  static async search(params: SearchMeditationParams): Promise<ApiResponse<MeditationListResponse>> {
    const queryParams = new URLSearchParams()
    
    queryParams.append('keyword', params.keyword)
    if (params.type) queryParams.append('type', params.type)
    if (params.pageNum) queryParams.append('pageNum', params.pageNum.toString())
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
    
    const queryString = queryParams.toString()
    const url = `/api/meditation/search?${queryString}`
    
    return api.get<MeditationListResponse>(url)
  }

  // 获取冥想标签列表
  static async getTags(): Promise<ApiResponse<MeditationTag[]>> {
    return api.get<MeditationTag[]>('/api/meditation/tags')
  }
}

// 便捷方法导出
export const meditationApi = MeditationApi
